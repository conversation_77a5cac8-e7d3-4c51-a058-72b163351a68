<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="dancing_script" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\font\dancing_script.ttf" qualifiers="" type="font"/><file name="playfair_display" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\font\playfair_display.ttf" qualifiers="" type="font"/><file name="activity_main" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="clouds" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\raw\clouds.jpeg" qualifiers="" type="raw"/><file name="stars" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\raw\stars.gif" qualifiers="" type="raw"/><file path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Rüya Analiz</string><string name="menu_home">AnaSayfa</string><string name="menu_dictionary">Rüya Tabirleri Sözlüğü</string><string name="menu_about">Hakkımızda</string><string name="menu_support">Destek</string><string name="menu_settings">Ayarlar</string><string name="menu_exit">Çıkış</string><string name="settings_title">Ayarlar</string><string name="settings_theme">Tema</string><string name="settings_theme_light">Açık Tema</string><string name="settings_theme_dark">Koyu Tema</string><string name="settings_language">Dil</string><string name="home_title">Rüyanızı Anlatın</string><string name="home_input_hint">Lütfen rüyanızı detaylı bir şekilde anlatın...</string><string name="home_analyze_button">✨ Rüyayı Yorumla ✨</string><string name="home_analyzing">✨ Rüyanız Analiz Ediliyor...</string><string name="home_min_chars">Lütfen en az %d karakter girin</string><string name="home_chars_sufficient">✨ Yeterli uzunlukta</string><string name="home_chars_count">%d karakter</string><string name="tell_your_dream">Tell Your Dream</string><string name="enter_dream_detail">Please describe your dream in detail...</string><string name="min_char_warning">Please enter at least 50 characters</string><string name="characters">characters</string><string name="interpret_dream">✨ Interpret Dream ✨</string><string name="analyzing_dream">✨ Analyzing Your Dream...</string><string name="dream_analyzed">✨ Your Dream Has Been Analyzed ✨</string><string name="dream_ready">Your Dream Interpretation is Ready</string><string name="click_to_see">Click to see</string><string name="dictionary_title">Dream Dictionary</string><string name="dictionary_search">Search</string><string name="dictionary_select_letter">Select Letter</string><string name="dictionary_no_internet">Offline Mode</string><string name="dictionary_dream_prefix">Seeing</string><string name="dialog_ok">OK</string><string name="in_dream">in dream</string><string name="support_title">Support / Feedback</string><string name="support_contact">Contact Us</string><string name="support_email">Email</string><string name="support_message">Message</string><string name="support_send">Send</string></file><file path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.YapayZekaRüyaAnaliz" parent="android:Theme.Material.Light.NoActionBar"/></file><file path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\values-ar\strings.xml" qualifiers="ar"><string name="app_name">تحليل الأحلام</string><string name="menu_home">الصفحة الرئيسية</string><string name="menu_dictionary">قاموس تفسير الأحلام</string><string name="menu_about">حول التطبيق</string><string name="menu_support">الدعم</string><string name="menu_settings">الإعدادات</string><string name="menu_exit">خروج</string><string name="settings_title">الإعدادات</string><string name="settings_theme">المظهر</string><string name="settings_theme_light">المظهر الفاتح</string><string name="settings_theme_dark">المظهر الداكن</string><string name="settings_language">اللغة</string><string name="home_title">احكي حلمك</string><string name="home_input_hint">يرجى وصف حلمك بالتفصيل...</string><string name="home_analyze_button">✨ تفسير الحلم ✨</string><string name="home_analyzing">✨ جاري تحليل حلمك...</string><string name="home_min_chars">الرجاء إدخال %d حرف على الأقل</string><string name="home_chars_sufficient">✨ طول كافي</string><string name="home_chars_count">%d حرف</string><string name="tell_your_dream">أخبرنا عن حلمك</string><string name="enter_dream_detail">يرجى وصف حلمك بالتفصيل...</string><string name="min_char_warning">الرجاء إدخال 50 حرفًا على الأقل</string><string name="characters">حروف</string><string name="interpret_dream">✨ تفسير الحلم ✨</string><string name="analyzing_dream">✨ جاري تحليل حلمك... ✨</string><string name="dream_analyzed">✨ تم تحليل حلمك ✨</string><string name="dream_ready">تفسير حلمك جاهز</string><string name="click_to_see">انقر للمشاهدة</string><string name="dictionary_title">تفسير الأحلام</string><string name="dictionary_search">بحث</string><string name="dictionary_select_letter">اختر الحرف</string><string name="dictionary_no_internet">وضع غير متصل</string><string name="dictionary_dream_prefix">رؤية</string><string name="dialog_ok">موافق</string><string name="in_dream">في المنام</string><string name="support_title">الدعم / التعليقات</string><string name="support_contact">اتصل بنا</string><string name="support_email">البريد الإلكتروني</string><string name="support_message">الرسالة</string><string name="support_send">إرسال</string></file><file path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">Dream Analysis</string><string name="menu_home">Home</string><string name="menu_dictionary">Dream Dictionary</string><string name="menu_about">About</string><string name="menu_support">Support</string><string name="menu_settings">Settings</string><string name="menu_exit">Exit</string><string name="settings_title">Settings</string><string name="settings_theme">Theme</string><string name="settings_theme_light">Light Theme</string><string name="settings_theme_dark">Dark Theme</string><string name="settings_language">Language</string><string name="home_title">Tell Your Dream</string><string name="home_input_hint">Please describe your dream in detail...</string><string name="home_analyze_button">✨ Interpret Dream ✨</string><string name="home_analyzing">✨ Analyzing Your Dream...</string><string name="home_min_chars">Please enter at least %d characters</string><string name="home_chars_sufficient">✨ Sufficient length</string><string name="home_chars_count">%d characters</string></file><file path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\values-tr\strings.xml" qualifiers="tr"><string name="tell_your_dream">Rüyanızı Anlatın</string><string name="enter_dream_detail">Lütfen rüyanızı detaylı bir şekilde anlatın...</string><string name="min_char_warning">Lütfen en az 50 karakter girin</string><string name="characters">karakter</string><string name="interpret_dream">✨ Rüyayı Yorumla ✨</string><string name="analyzing_dream">✨ Rüyanız Analiz Ediliyor... ✨</string><string name="dream_analyzed">✨ Rüyanız Analiz Edildi ✨</string><string name="dream_ready">Rüya Tabiriniz Hazır</string><string name="click_to_see">Görmek için tıklayınız</string><string name="dictionary_title">Rüya Tabirleri Sözlüğü</string><string name="dictionary_search">Ara</string><string name="dictionary_select_letter">Harf Seçin</string><string name="dictionary_no_internet">Çevrimdışı Mod</string><string name="dictionary_dream_prefix">Rüyada</string><string name="dialog_ok">Tamam</string><string name="in_dream">görmek</string><string name="support_title">Destek / Geri Bildirim</string><string name="support_contact">Bize Ulaşın</string><string name="support_email">E-posta</string><string name="support_message">Mesaj</string><string name="support_send">Gönder</string></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="scroll" path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\main\res\drawable\scroll.png" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\ver10 Checkpoint\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>