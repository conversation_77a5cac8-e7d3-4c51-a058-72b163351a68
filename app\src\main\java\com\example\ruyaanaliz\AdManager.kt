package com.example.ruyaanaliz

import android.app.Activity
import android.content.Context
import android.util.Log
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback

class AdManager(private val context: Context) {
    private val TAG = "AdManager"
    private var interstitialAd: InterstitialAd? = null
    
    // Test reklamı için kimlik
    private val TEST_AD_UNIT_ID = "ca-app-pub-3940256099942544/1033173712"
    
    // Gerçek reklam kimliği (ileride kullanılacak)
    private val REAL_AD_UNIT_ID = "ca-app-pub-6304761417427765/9836491407"
    
    // Şu an için test reklamı kullanıyoruz
    private val adUnitId = TEST_AD_UNIT_ID
    
    fun loadInterstitialAd(onAdLoaded: () -> Unit = {}) {
        InterstitialAd.load(
            context,
            adUnitId,
            AdRequest.Builder().build(),
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(ad: InterstitialAd) {
                    Log.d(TAG, "Geçiş reklamı yüklendi.")
                    interstitialAd = ad
                    onAdLoaded()
                }

                override fun onAdFailedToLoad(adError: LoadAdError) {
                    Log.d(TAG, "Geçiş reklamı yüklenemedi: ${adError.message}")
                    interstitialAd = null
                }
            }
        )
    }
    
    fun showInterstitialAd(
        activity: Activity,
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: () -> Unit = {}
    ) {
        if (interstitialAd != null) {
            interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Reklam kapatıldı.")
                    // Reklam kapatıldığında callback'i çağır
                    onAdDismissed()
                    // Yeni reklam yükle
                    loadInterstitialAd()
                }

                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    Log.d(TAG, "Reklam gösterilemedi: ${adError.message}")
                    interstitialAd = null
                    onAdFailedToShow()
                }

                override fun onAdShowedFullScreenContent() {
                    Log.d(TAG, "Reklam gösterildi.")
                    interstitialAd = null
                }
            }
            interstitialAd?.show(activity)
        } else {
            Log.d(TAG, "Gösterilecek reklam yok, yeniden yükleniyor.")
            onAdFailedToShow()
            loadInterstitialAd()
        }
    }
}

