package com.lacrestudio.yapayzekaruya

import android.app.Activity
import android.content.Context

// AdManager sınıfı devre dışı bırakıldı
class AdManager(private val context: Context) {
    // Boş bir sınıf haline getirildi
    
    fun loadInterstitialAd(onAdLoaded: () -> Unit = {}) {
        // Boş implementasyon
        onAdLoaded()
    }
    
    fun showInterstitialAd(
        activity: Activity,
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: () -> Unit = {}
    ) {
        // Direkt olarak başarısız olduğunu bildir
        onAdFailedToShow()
    }
}
