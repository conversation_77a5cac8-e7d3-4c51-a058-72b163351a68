package com.example.ruyaanaliz.api

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

private const val API_KEY = "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"

interface OpenAIService {
    @Headers(
        "Content-Type: application/json",
        "Authorization: Bearer $API_KEY",
        "HTTP-Referer: com.example.ruyaanaliz",
        "X-Title: Ruya"
    )
    @POST("v1/chat/completions")
    suspend fun generateResponse(@Body request: ChatRequest): Response<ChatResponse>

    suspend fun analyzeRüya(rüyaMetni: String): String {
        val request = ChatRequest(
            model = "qwen/qwen-2.5-coder-32b-instruct",
            messages = listOf(
                Message(
                    role = "system",
                    content = "Sen profesyonel bir rüya yorumcususun. Verilen rüyayı kısa bir şekilde analiz et ve yorumla. Rüyanın psikolojik ve sembolik anlamlarını açıkla. Yorumunu kısaca madde madde yap ve sonunda genel bir değerlendirme ekle. rüyaya yapacağın analizin uzunluğu anlatılan rüya ile paralel olarak uzasın."
                ),
                Message(
                    role = "user",
                    content = rüyaMetni
                )
            )
        )

        val response = generateResponse(request)
        return if (response.isSuccessful) {
            response.body()?.choices?.firstOrNull()?.message?.content ?: "Üzgünüm, rüyanızı yorumlayamadım."
        } else {
            "Rüya analizi sırasında bir hata oluştu"
        }
    }
}

data class ChatRequest(
    val model: String,
    val messages: List<Message>
)

data class Message(
    val role: String,
    val content: String
)

data class ChatResponse(
    val choices: List<Choice>
)

data class Choice(
    val message: Message
) 
