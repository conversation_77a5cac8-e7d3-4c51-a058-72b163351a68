package com.example.ruyaanaliz

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.TextStyle
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.decode.ImageDecoderDecoder
import kotlinx.coroutines.launch
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import com.example.ruyaanaliz.api.*
import com.example.ruyaanaliz.menu.AyarlarEkrani
import com.example.ruyaanaliz.menu.SozlukEkrani
import com.example.ruyaanaliz.menu.DestekEkrani
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import android.content.Context
import android.content.SharedPreferences
import android.app.Activity
import android.content.res.Configuration
import java.util.*
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.compose.material.icons.filled.AutoAwesome
import android.content.Intent
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.LaunchedEffect
import androidx.core.content.ContextCompat
import android.content.pm.PackageManager
import androidx.compose.animation.core.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Offset
import kotlin.random.Random
import androidx.compose.animation.core.Animatable
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import kotlinx.coroutines.delay
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import java.net.URLDecoder
import kotlinx.coroutines.CoroutineScope
import androidx.core.content.edit
import com.google.android.gms.ads.MobileAds
import kotlinx.coroutines.Dispatchers
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.unit.sp

val dancingScript = FontFamily(Font(R.font.dancing_script))
val playfair = FontFamily(Font(R.font.playfair_display))

@Composable
fun StylishTitle(text: String) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 24.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "✧ ✦ ✧",
                style = TextStyle(
                    fontSize = 24.sp,
                    color = MaterialTheme.colorScheme.primary,
                    fontFamily = dancingScript
                ),
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = text,
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontFamily = playfair,
                    fontWeight = FontWeight.Normal,
                    fontSize = 32.sp,
                    letterSpacing = 1.sp
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            Text(
                text = "✧ ✦ ✧",
                style = TextStyle(
                    fontSize = 24.sp,
                    color = MaterialTheme.colorScheme.primary,
                    fontFamily = dancingScript
                ),
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
fun BackgroundImage(isDarkMode: Boolean) {
    val context = LocalContext.current
    val imageRes = if (isDarkMode) R.raw.stars else R.raw.clouds
    val imageDescription = if (isDarkMode) "Yıldızlı Arkaplan" else "Bulutlu Arkaplan"

    Image(
        painter = rememberAsyncImagePainter(
            ImageRequest.Builder(context)
                .data(imageRes)
                .decoderFactory(ImageDecoderDecoder.Factory())
                .build()
        ),
        contentDescription = imageDescription,
        modifier = Modifier.fillMaxSize(),
        contentScale = ContentScale.Crop
    )
}

@Composable
fun ScrollBackground() {
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp
    val scrollImageHeight = 400 // Parşömen görselinin yüksekliği (dp cinsinden)
    val repeatCount = (screenHeight / scrollImageHeight + 2) // Ekranı kaplayacak kadar tekrar

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        repeat(repeatCount) {
            Image(
                painter = painterResource(id = R.drawable.scroll),
                contentDescription = "Parşömen Arka Plan",
                modifier = Modifier
                    .fillMaxWidth()
                    .height(scrollImageHeight.dp)
                    .alpha(0.25f),
                contentScale = ContentScale.FillBounds
            )
        }
    }
}

@Composable
fun ModernYorumlaButonu(
    isLoading: Boolean,
    isInputValid: Boolean,
    onClick: () -> Unit
) {
    // Butona tıklandığında hafif bir animasyon ekleyelim
    val buttonScale by animateFloatAsState(
        targetValue = if (isLoading) 0.95f else 1f,
        animationSpec = tween(durationMillis = 200)
    )

    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
            .clip(RoundedCornerShape(16.dp)) // Köşeleri yuvarlak yap
            .background(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFF6A1B9A), // Mor renk
                        Color(0xFFAB47BC)  // Açık mor renk
                    )
                )
            ),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent, // Arka plan rengini şeffaf yap
            contentColor = Color.White // Yazı ve ikon rengi
        ),
        enabled = isInputValid && !isLoading
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = Color.White
                )
            } else {
                Icon(
                    imageVector = Icons.Default.AutoAwesome, // Şık bir ikon
                    contentDescription = "Yorumla",
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Rüyayı Yorumla",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                )
            }
        }
    }
}

class MainActivity : ComponentActivity() {
    private lateinit var openAIService: OpenAIService
    private lateinit var prefs: SharedPreferences
    lateinit var adManager: AdManager

    override fun attachBaseContext(newBase: Context) {
        val sharedPrefs = newBase.getSharedPreferences("AppSettings", MODE_PRIVATE)
        val language = sharedPrefs.getString("language", "Türkçe") ?: "Türkçe"
        val locale = when (language) {
            "Türkçe" -> Locale("tr")
            "English" -> Locale("en")
            "العربية" -> Locale("ar")
            else -> Locale("tr")
        }

        val config = Configuration(newBase.resources.configuration)
        config.setLocale(locale)
        Locale.setDefault(locale)

        val context = newBase.createConfigurationContext(config)
        super.attachBaseContext(context)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupOpenAIService()

        // AdManager'ı başlat
        adManager = AdManager(this)
        
        // MobileAds'i arka planda başlat
        val backgroundScope = CoroutineScope(Dispatchers.IO)
        backgroundScope.launch {
            MobileAds.initialize(this@MainActivity) {
                // MobileAds başlatıldıktan sonra ilk reklamı yükle
                adManager.loadInterstitialAd()
            }
        }

        prefs = getSharedPreferences("AppSettings", MODE_PRIVATE)
        val savedLanguage = prefs.getString("language", "Türkçe") ?: "Türkçe"
        val savedTheme = prefs.getBoolean("darkMode", false)

        setContent {
            var isDarkMode by remember { mutableStateOf(savedTheme) }
            var currentLanguage by remember { mutableStateOf(savedLanguage) }
            val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)
            val scope = rememberCoroutineScope()
            val navController = rememberNavController()
            var currentScreen by remember { mutableStateOf("home") }

            val menuHome = stringResource(R.string.menu_home)
            val menuDictionary = stringResource(R.string.menu_dictionary)
            val menuSupport = stringResource(R.string.menu_support)
            val menuSettings = stringResource(R.string.menu_settings)

            val screens = listOf(
                menuHome to Icons.Default.Home,
                menuDictionary to Icons.Default.DateRange,
                menuSupport to Icons.Default.Email,
                menuSettings to Icons.Default.Settings
            )

            MaterialTheme(
                colorScheme = if (isDarkMode) {
                    darkColorScheme()
                } else {
                    lightColorScheme()
                }
            ) {
                ModalNavigationDrawer(
                    drawerState = drawerState,
                    drawerContent = {
                        Surface(
                            color = if (isDarkMode) Color(0xFF1E1E1E) else Color(0xFFF5F5F5) // Arka plan rengi
                        ) {
                            ModalDrawerSheet {
                                Spacer(modifier = Modifier.height(24.dp))
                                Text(
                                    text = stringResource(R.string.app_name),
                                    modifier = Modifier.padding(16.dp),
                                    style = MaterialTheme.typography.headlineMedium.copy(
                                        fontFamily = playfair,
                                        fontWeight = FontWeight.Bold,
                                        color = if (isDarkMode) Color.White else Color.Black // Başlık rengi
                                    )
                                )
                                HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))
                                screens.forEach { (screen, icon) ->
                                    NavigationDrawerItem(
                                        icon = { Icon(icon, contentDescription = null) },
                                        label = { Text(text = screen) },
                                        selected = when (currentScreen) {
                                            "home" -> screen == menuHome
                                            "dictionary" -> screen == menuDictionary
                                            "support" -> screen == menuSupport
                                            "settings" -> screen == menuSettings
                                            else -> false
                                        },
                                        onClick = {
                                            scope.launch {
                                                drawerState.close()
                                                currentScreen = when (screen) {
                                                    menuHome -> "home"
                                                    menuDictionary -> "dictionary"
                                                    menuSupport -> "support"
                                                    menuSettings -> "settings"
                                                    else -> "home"
                                                }
                                            }
                                        },
                                        colors = NavigationDrawerItemDefaults.colors(
                                            selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                            unselectedContainerColor = Color.Transparent,
                                            selectedTextColor = MaterialTheme.colorScheme.onPrimaryContainer,
                                            unselectedTextColor = if (isDarkMode) Color.White else Color.Black
                                        )
                                    )
                                }
                                NavigationDrawerItem(
                                    icon = {
                                        Icon(
                                            Icons.AutoMirrored.Filled.ExitToApp,
                                            contentDescription = null
                                        )
                                    },
                                    label = { Text(text = stringResource(R.string.menu_exit)) },
                                    selected = false,
                                    onClick = {
                                        scope.launch {
                                            drawerState.close()
                                            finish()
                                        }
                                    },
                                    colors = NavigationDrawerItemDefaults.colors(
                                        selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                        unselectedContainerColor = Color.Transparent,
                                        selectedTextColor = MaterialTheme.colorScheme.onPrimaryContainer,
                                        unselectedTextColor = if (isDarkMode) Color.White else Color.Black
                                    )
                                )
                            }
                        }
                    }
                ) {

                    NavHost(navController = navController, startDestination = "giris") {
                        composable("giris") {
                            when (currentScreen) {
                                "home" -> AnaEkran(
                                    navController = navController,
                                    onMenuClick = { scope.launch { drawerState.open() } },
                                    openAIService = openAIService,
                                    scope = scope,
                                    isDarkMode = isDarkMode,
                                    onThemeChange = { newTheme ->
                                        isDarkMode = newTheme
                                        prefs.edit().putBoolean("darkMode", newTheme).apply()
                                    }
                                )
                                "dictionary" -> SozlukEkrani(
                                    onMenuClick = { scope.launch { drawerState.open() } },
                                    isDarkMode = isDarkMode,
                                    onThemeChange = { newTheme ->
                                        isDarkMode = newTheme
                                        prefs.edit { putBoolean("darkMode", newTheme) }
                                    }
                                )
                                "support" -> DestekEkrani(
                                    onMenuClick = { scope.launch { drawerState.open() } }
                                )
                                "settings" -> AyarlarEkrani(
                                    onMenuClick = { scope.launch { drawerState.open() } },
                                    isDarkMode = isDarkMode,
                                    onThemeChange = { newTheme ->
                                        isDarkMode = newTheme
                                        prefs.edit().putBoolean("darkMode", newTheme).apply()
                                    },
                                    currentLanguage = currentLanguage,
                                    onLanguageChange = { newLanguage ->
                                        currentLanguage = newLanguage
                                        prefs.edit().putString("language", newLanguage).apply()
                                        val intent = intent
                                        finish()
                                        startActivity(intent)
                                        overridePendingTransition(0, 0)
                                    }
                                )
                            }
                        }
                        composable(
                            route = "analiz_tamamlandi/{sonuc}",
                            arguments = listOf(
                                navArgument("sonuc") { type = NavType.StringType }
                            )
                        ) { backStackEntry ->
                            val sonuc = backStackEntry.arguments?.getString("sonuc") ?: ""
                            AnalizTamamlandiEkrani(
                                sonuc = URLDecoder.decode(sonuc, StandardCharsets.UTF_8.toString()),
                                onSonucuGosterClick = { decodedSonuc ->
                                    val encodedSonuc = URLEncoder.encode(decodedSonuc, StandardCharsets.UTF_8.toString())
                                    navController.navigate("sonuc/$encodedSonuc") {
                                        popUpTo("analiz_tamamlandi") { inclusive = true }
                                    }
                                },
                                onMenuClick = {
                                    currentScreen = "home"
                                    scope.launch { drawerState.open() }
                                }, // Menü butonu için
                                onThemeChange = { newTheme ->
                                    isDarkMode = newTheme
                                    prefs.edit().putBoolean("darkMode", newTheme).apply()
                                },
                                isDarkMode = isDarkMode // Tema durumu
                            )
                        }
                        composable(
                            route = "sonuc/{sonuc}",
                            arguments = listOf(
                                navArgument("sonuc") { type = NavType.StringType }
                            )
                        ) { backStackEntry ->
                            val sonuc = backStackEntry.arguments?.getString("sonuc") ?: ""
                            RuyaSonucEkrani(
                                sonuc = URLDecoder.decode(sonuc, StandardCharsets.UTF_8.toString()),
                                onYeniRuyaClick = {
                                    navController.navigate("giris") {
                                        popUpTo("giris") { inclusive = true }
                                    }
                                },
                                onMenuClick = {
                                    currentScreen = "home"
                                    scope.launch { drawerState.open() }
                                }, // Menü butonu icin
                                onThemeChange = { newTheme ->
                                    isDarkMode = newTheme
                                    prefs.edit().putBoolean("darkMode", newTheme).apply()
                                },
                                isDarkMode = isDarkMode // Tema durumu
                            )

                        }
                    }
                }
            }
        }
    }

    private fun setupOpenAIService() {
        val retrofit = Retrofit.Builder()
            .baseUrl("https://openrouter.ai/api/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        openAIService = retrofit.create(OpenAIService::class.java)
    }
}

@Composable
fun AnalizTamamlandiEkrani(
    sonuc: String,
    onSonucuGosterClick: (String) -> Unit,
    onMenuClick: () -> Unit,
    onThemeChange: (Boolean) -> Unit,
    isDarkMode: Boolean
) {
    val context = LocalContext.current
    val activity = context as? Activity
    val adManager = remember { (context as MainActivity).adManager }

    Box(modifier = Modifier.fillMaxSize()) {
        BackgroundImage(isDarkMode)
        
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.Transparent
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                StylishTitle(text = "Rüya Analizi Tamamlandı")

                Button(
                    onClick = {
                        activity?.let { act ->
                            adManager.showInterstitialAd(
                                activity = act,
                                onAdDismissed = {
                                    onSonucuGosterClick(sonuc)
                                },
                                onAdFailedToShow = {
                                    onSonucuGosterClick(sonuc)
                                }
                            )
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.weight(1f) // Metinleri sola yaslamak için ağırlık veriyoruz
                        ) {
                            Text(
                                text = "  ✨ Rüya Yorumunuz Hazır ✨",
                                style = MaterialTheme.typography.titleLarge.copy(
                                    fontFamily = playfair,
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 20.sp,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                            )
                            Text(
                                text = "  Görmek için tıklayın",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 16.sp,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                ),
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                        Text(
                            text = "\uD83C\uDFAC", // 🎬 Emoji
                            style = MaterialTheme.typography.bodySmall.copy(fontSize = 30.sp),
                            modifier = Modifier.padding(end = 6.dp) // Sağ tarafa boşluk ekleyerek hizalama
                        )
                    }
                }
                    }
                }
            }
        }


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnaEkran(
    navController: NavController,
    onMenuClick: () -> Unit,
    openAIService: OpenAIService,
    scope: CoroutineScope,
    isDarkMode: Boolean,
    onThemeChange: (Boolean) -> Unit
) {
    var ruya by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }
    // Dinleme durumunu takip etmek için yeni bir state ekleyelim
    var isListening by remember { mutableStateOf(false) }
    val minCharacterLimit = 25
    val isInputValid = ruya.length >= minCharacterLimit
    val context = LocalContext.current

    // SpeechRecognizer ve Intent
    val speechRecognizer = remember { SpeechRecognizer.createSpeechRecognizer(context) }
    val speechIntent = remember {
        Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, "tr-TR")
        }
    }

    // SpeechRecognizer dinleyici tanımı
    val recognitionListener = remember {
        object : RecognitionListener {
            override fun onReadyForSpeech(params: Bundle?) {
                // Konuşma dinlemeye hazır olduğunda dinleme durumunu true yap
                isListening = true
            }
            override fun onBeginningOfSpeech() {}
            override fun onRmsChanged(rmsdB: Float) {}
            override fun onBufferReceived(buffer: ByteArray?) {}
            override fun onEndOfSpeech() {
                // Konuşma bittiğinde dinleme durumunu false yap
                isListening = false
            }
            override fun onError(error: Int) {
                // Hata durumunda dinlemeyi kapat
                isListening = false
                Toast.makeText(context, "Ses algılama hatası: $error", Toast.LENGTH_SHORT).show()
            }

            override fun onResults(results: Bundle?) {
                // Sonuçlar geldiğinde dinlemeyi kapat
                isListening = false
                val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    ruya = matches[0]
                }
            }

            override fun onPartialResults(partialResults: Bundle?) {}
            override fun onEvent(eventType: Int, params: Bundle?) {}
        }
    }

    fun startSpeechRecognition() {
        speechRecognizer.setRecognitionListener(recognitionListener)
        speechRecognizer.startListening(speechIntent)
    }

    // Mikrofon izinleri
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startSpeechRecognition()
        } else {
            Toast.makeText(context, "Mikrofon izni gereklidir", Toast.LENGTH_SHORT).show()
        }
    }

    fun checkAndRequestPermission() {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED) {
            startSpeechRecognition()
        } else {
            permissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            speechRecognizer.destroy()
        }
    }

    // Dinleme ekranını gösterme
    DinlemeEkrani(
        isListening = isListening,
        onClose = {
            isListening = false
            speechRecognizer.stopListening()
        }
    )

    Box(modifier = Modifier.fillMaxSize()) {
        BackgroundImage(isDarkMode)

        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.Transparent
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Üst Bar
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    FilledTonalButton(
                        onClick = onMenuClick,
                        modifier = Modifier.size(48.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                        ),
                        contentPadding = PaddingValues(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Menu,
                            contentDescription = "Menü",
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    FilledTonalButton(
                        onClick = { onThemeChange(!isDarkMode) },
                        modifier = Modifier.size(48.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                        ),
                        contentPadding = PaddingValues(8.dp)
                    ) {
                        Text(
                            text = if (isDarkMode) "🌙" else "☀️",
                            fontSize = 20.sp
                        )
                    }
                }

                // Ana İçerik
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "✨ Rüya Yorumlayıcı ✨",
                        style = MaterialTheme.typography.displaySmall.copy(
                            fontFamily = dancingScript,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onBackground
                        ),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 32.dp)
                    )

                    // Rüya Giriş Alanı ve Mikrofon Butonu
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = ruya,
                            onValueChange = { ruya = it },
                            label = { Text("Lütfen rüyanı detaylı bir şekilde anlat... )") },
                            modifier = Modifier
                                .weight(1f)
                                .padding(end = 8.dp),
                            shape = RoundedCornerShape(12.dp),
                            minLines = 4,
                            enabled = !isLoading && !isListening,
                            colors = TextFieldDefaults.outlinedTextFieldColors(
                                containerColor = if (isDarkMode) Color.Black.copy(alpha = 0.2f) else Color.White.copy(alpha = 0.5f),
                                focusedBorderColor = MaterialTheme.colorScheme.primary,
                                unfocusedBorderColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f),
                                cursorColor = if (isDarkMode) Color.White else Color.Black,
                                focusedTextColor = if (isDarkMode) Color.White else Color.Black,
                                unfocusedTextColor = if (isDarkMode) Color.White.copy(alpha = 0.8f) else Color.Black.copy(alpha = 0.8f)
                            ),
                            supportingText = {
                                Text(
                                    text = "${ruya.length}/$minCharacterLimit karakter",
                                    color = if (isInputValid) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                                )
                            },
                            isError = ruya.isNotEmpty() && !isInputValid
                        )

                        // Mikrofon Butonu - Dinleme durumunda renk ve ikon değişimi
                        IconButton(
                            onClick = {
                                if (!isListening) {
                                    checkAndRequestPermission()
                                } else {
                                    speechRecognizer.stopListening()
                                    isListening = false
                                }
                            },
                            modifier = Modifier
                                .size(56.dp)
                                .background(
                                    color = if (isListening) Color(0xFFE91E63) else MaterialTheme.colorScheme.primary,
                                    shape = RoundedCornerShape(12.dp)
                                )
                        ) {
                            Icon(
                                imageVector = if (isListening) Icons.Default.Close else Icons.Default.Mic,
                                contentDescription = if (isListening) "Dinlemeyi Durdur" else "Mikrofon",
                                tint = Color.White,
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }

                    ModernYorumlaButonu(
                        isLoading = isLoading,
                        isInputValid = isInputValid,
                        onClick = {
                            isLoading = true
                            scope.launch {
                                try {
                                    val request = ChatRequest(
                                        model = "qwen/qwen-2.5-coder-32b-instruct",
                                        messages = listOf(
                                            Message(
                                                role = "system",
                                                content = "Sen profesyonel bir rüya yorumcususun. Verilen rüyayı detaylı bir şekilde analiz et ve yorumla. Rüyanın psikolojik ve sembolik anlamlarını açıkla. Yorumunu madde madde yap ve sonunda genel bir değerlendirme ekle."
                                            ),
                                            Message(
                                                role = "user",
                                                content = "Rüyamda $ruya"
                                            )
                                        )
                                    )

                                    val response = openAIService.generateResponse(request)
                                    if (response.isSuccessful) {
                                        val yorumSonucu = response.body()?.choices?.firstOrNull()?.message?.content ?: "Üzgünüm, rüyanızı yorumlayamadım."
                                        val encodedSonuc = URLEncoder.encode(yorumSonucu, StandardCharsets.UTF_8.toString())
                                        navController.navigate("analiz_tamamlandi/$encodedSonuc")
                                    } else {
                                        val errorMessage = "Bir hata oluştu: ${response.message()}"
                                        val encodedError = URLEncoder.encode(errorMessage, StandardCharsets.UTF_8.toString())
                                        navController.navigate("sonuc/$encodedError")
                                    }
                                } catch (e: Exception) {
                                    val errorMessage = "Bir hata oluştu: ${e.message}"
                                    val encodedError = URLEncoder.encode(errorMessage, StandardCharsets.UTF_8.toString())
                                    navController.navigate("sonuc/$encodedError")
                                } finally {
                                    isLoading = false
                                }
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun RuyaSonucEkrani(
    sonuc: String,
    onYeniRuyaClick: () -> Unit,
    onMenuClick: () -> Unit, // Menü butonu icin onClick parametresi
    onThemeChange: (Boolean) -> Unit, // Tema butonu icin onClick parametresi
    isDarkMode: Boolean // Tema butonu icin tema durumu
) {
    val scrollState = rememberScrollState()

    Box(modifier = Modifier.fillMaxSize()) {
        BackgroundImage(isDarkMode)

        // Parşömen arka plan - tekrar eden pattern
        ScrollBackground()

        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.Transparent
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Üst Bar (Menü ve Tema Butonları)
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Menü Butonu
                    FilledTonalButton(
                        onClick = onMenuClick,
                        modifier = Modifier.size(48.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                        ),
                        contentPadding = PaddingValues(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Menu,
                            contentDescription = "Menu",
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    // Tema Butonu
                    FilledTonalButton(
                        onClick = { onThemeChange(!isDarkMode) },
                        modifier = Modifier.size(48.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                        ),
                        contentPadding = PaddingValues(8.dp)
                    ) {
                        Text(
                            text = if (isDarkMode) "🌙" else "☀️",
                            fontSize = 20.sp
                        )
                    }
                }

                // Ruya Sonuc İçeriği - Transparan arka plan ile
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            color = MaterialTheme.colorScheme.surface.copy(alpha = 0.35f),
                            shape = RoundedCornerShape(16.dp)
                        )
                        .padding(20.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .verticalScroll(scrollState)
                    ) {
                        StylishTitle(text = "Rüya Tabiriniz")

                        Text(
                            text = sonuc,
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontFamily = playfair,
                                fontSize = 18.sp,
                                lineHeight = 28.sp,
                                letterSpacing = 0.3.sp,
                                color = MaterialTheme.colorScheme.onSurface
                            ),
                            modifier = Modifier.padding(bottom = 24.dp)
                        )

                        ElevatedButton(
                            onClick = onYeniRuyaClick,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            colors = ButtonDefaults.elevatedButtonColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer,
                                contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                            ),
                            elevation = ButtonDefaults.elevatedButtonElevation(
                                defaultElevation = 6.dp,
                                pressedElevation = 8.dp
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text(
                                "✨ Başka Bir Rüya Sor ✨",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontFamily = dancingScript,
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 20.sp
                                ),
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DestekEkrani(onMenuClick: () -> Unit) {
    val isDarkMode = MaterialTheme.colorScheme == darkColorScheme()

    Box(modifier = Modifier.fillMaxSize()) {
        BackgroundImage(isDarkMode)

        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.Transparent // Arka plan rengini şeffaf yap
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    FilledTonalButton(
                        onClick = onMenuClick,
                        modifier = Modifier.size(56.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                        ),
                        contentPadding = PaddingValues(8.dp)
                    ) {
                        Icon(
                            Icons.Default.Menu,
                            contentDescription = "Menu",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                StylishTitle("Geri Bildirim / Destek")
                // İçerik daha sonra eklenecek
            }
        }
    }
}

@Composable
fun AyarlarEkrani(
    onMenuClick: () -> Unit,
    isDarkMode: Boolean,
) {
    Box(modifier = Modifier.fillMaxSize()) {
        BackgroundImage(isDarkMode)

        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.Transparent // Arka plan rengini şeffaf yap
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    FilledTonalButton(
                        onClick = onMenuClick,
                        modifier = Modifier.size(56.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer,
                            contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                        ),
                        contentPadding = PaddingValues(8.dp)
                    ) {
                        Icon(
                            Icons.Default.Menu,
                            contentDescription = "Menu",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                StylishTitle("Ayarlar")
                // İçerik daha sonra eklenecek
            }
        }
    }
}

@Composable
fun DinlemeEkrani(
    isListening: Boolean,
    onClose: () -> Unit
) {
    if (isListening) {
        var animationValue by remember { mutableStateOf(0f) }
        val animatedValue by animateFloatAsState(
            targetValue = animationValue,
            animationSpec = tween(durationMillis = 1000, easing = LinearEasing)
        )

        LaunchedEffect(isListening) {
            while (isListening) {
                animationValue = if (animationValue < 1f) 1f else 0f
                delay(1000)
            }
        }

        Dialog(
            onDismissRequest = onClose,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            Box(
                modifier = Modifier
                    .size(300.dp)
                    .clip(RoundedCornerShape(24.dp))
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color(0xFF6A1B9A),
                                Color(0xFF3700B3)
                            ),
                            center = Offset.Infinite,
                            radius = 300f
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.padding(16.dp)
                ) {
                    // Mikrofon ikonu ve pulse efekti
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.padding(bottom = 16.dp)
                    ) {
                        repeat(3) { index ->
                            val pulseAlpha = remember { Animatable(0f) }
                            val pulseSize = remember { Animatable(0f) }

                            LaunchedEffect(isListening, index) {
                                delay(index * 400L)
                                launch {
                                    pulseAlpha.animateTo(
                                        targetValue = 0f,
                                        animationSpec = infiniteRepeatable(
                                            animation = keyframes {
                                                durationMillis = 2000
                                                0.7f at 0
                                                0f at 2000
                                            },
                                            repeatMode = RepeatMode.Restart
                                        )
                                    )
                                }
                                launch {
                                    pulseSize.animateTo(
                                        targetValue = 1f,
                                        animationSpec = infiniteRepeatable(
                                            animation = keyframes {
                                                durationMillis = 2000
                                                0f at 0
                                                1f at 2000
                                            },
                                            repeatMode = RepeatMode.Restart
                                        )
                                    )
                                }
                            }

                            Box(
                                modifier = Modifier
                                    .size(120.dp * pulseSize.value)
                                    .align(Alignment.Center)
                                    .alpha(pulseAlpha.value)
                                    .background(
                                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.4f),
                                        shape = CircleShape
                                    )
                            )
                        }

                        // Mikrofon simgesi
                        Icon(
                            imageVector = Icons.Default.Mic,
                            contentDescription = "Mikrofon",
                            tint = Color.White,
                            modifier = Modifier
                                .size(64.dp)
                                .padding(8.dp)
                                .background(
                                    color = MaterialTheme.colorScheme.primary,
                                    shape = CircleShape
                                )
                                .padding(12.dp)
                        )
                    }

                    // Ses dalga animasyonu
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(40.dp)
                            .padding(vertical = 8.dp)
                    ) {
                        val waveCount = 7
                        val itemWidth = 4.dp
                        val itemSpacing = 4.dp

                        repeat(waveCount) { index ->
                            val individualDelay = index * 100
                            val delay = remember { mutableStateOf(individualDelay) }

                            var waveHeight by remember { mutableStateOf(0.2f) }
                            val animatedHeight by animateFloatAsState(
                                targetValue = waveHeight,
                                animationSpec = infiniteRepeatable(
                                    animation = tween(
                                        durationMillis = 500,
                                        easing = FastOutSlowInEasing,
                                        delayMillis = delay.value
                                    ),
                                    repeatMode = RepeatMode.Reverse
                                )
                            )

                            LaunchedEffect(isListening) {
                                delay(individualDelay.toLong())
                                waveHeight = Random.nextFloat() * 0.8f + 0.2f
                            }

                            Box(
                                modifier = Modifier
                                    .width(itemWidth)
                                    .fillMaxHeight(animatedHeight)
                                    .background(
                                        color = Color.White,
                                        shape = RoundedCornerShape(4.dp)
                                    )
                            )

                            if (index < waveCount - 1) {
                                Spacer(modifier = Modifier.width(itemSpacing))
                            }
                        }
                    }

                    Text(
                        text = "Sizi Dinliyorum",
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontFamily = dancingScript,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        ),
                        modifier = Modifier.padding(vertical = 8.dp)
                    )

                    Text(
                        text = "Lütfen rüyanızı anlatın...",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            color = Color.White.copy(alpha = 0.8f)
                        ),
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    FilledTonalButton(
                        onClick = onClose,
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = Color.White.copy(alpha = 0.2f),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Text("İptal")
                    }
                }
            }
        }
    }
}










