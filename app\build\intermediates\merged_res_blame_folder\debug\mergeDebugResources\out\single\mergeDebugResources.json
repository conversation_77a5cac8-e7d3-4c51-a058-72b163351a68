[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\font_dancing_script.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\font\\dancing_script.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\raw_stars.gif.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\raw\\stars.gif"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\raw_clouds.jpeg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\raw\\clouds.jpeg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\font_playfair_display.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\font\\playfair_display.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "com.example.ruyaanaliz.app-debug-67:/drawable_scroll.png.flat", "source": "com.example.ruyaanaliz.app-main-69:/drawable/scroll.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-debug-67:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.ruyaanaliz.app-main-69:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}]